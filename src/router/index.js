import { createRouter, createWebHashHistory } from 'vue-router'
import Store from '@/store/index.js'
import AppApi from '@/api/sys/app.js'

const _routes = [
  // 使用管理页面布局
  {
    name: 'admin',
    path: '/',
    component: () => import('@/layouts/AdminTabLayout.vue'),
    children: [
      // 主页
      {
        name: 'admin.home',
        path: '/',
        component: () => import('@/views/Home.vue'),
        meta: {
          title: '主页',
          anon: false,
          keepAlive: true
        }
      },
      // Banner管理
      {
        name: 'admin.cms.banner.index',
        path: '/cms/banner',
        component: () => import('@/views/cms/Banner.vue'),
        meta: {
          title: '横幅管理',
          anon: false,
          keepAlive: true
        }
      },
      {
        name: 'admin.cms.banner.edit',
        path: '/cms/banner/edit',
        component: () => import('@/views/cms/ArticlePicker.vue'),
        meta: {
          title: '推荐编辑',
          anon: false
        }
      },
      // 资讯管理
      {
        name: 'admin.cms.article.index',
        path: '/cms/article',
        component: () => import('@/views/cms/Article.vue'),
        meta: {
          title: '资讯管理',
          anon: false,
          keepAlive: true
        }
      },
      {
        name: 'admin.cms.article.editor',
        path: '/cms/article/edit',
        component: () => import('@/views/cms/ArticleEditor.vue'),
        meta: {
          title: '资讯编辑',
          anon: false
        }
      },
      // 活动管理
      {
        name: 'admin.cms.activity.index',
        path: '/cms/activity',
        component: () => import('@/views/cms/Activity.vue'),
        meta: {
          title: '活动管理',
          anon: false,
          keepAlive: true
        }
      },
      {
        name: 'admin.cms.activity.editor',
        path: '/cms/activity/edit',
        component: () => import('@/views/cms/ActivityEditor.vue'),
        meta: {
          title: '活动编辑',
          anon: false
        }
      },
      {
        name: 'admin.cms.activity.participant',
        path: '/cms/activity/participant',
        component: () => import('@/views/cms/Participant.vue'),
        meta: {
          title: '报名记录',
          anon: false
        }
      },
      // 事项管理
      {
        name: 'admin.service.guide.index',
        path: '/service/guide',
        component: () => import('@/views/service/Guide.vue'),
        meta: {
          title: '事项管理',
          anon: false,
          keepAlive: true
        }
      },
      {
        name: 'admin.service.guide.editor',
        path: '/service/guide/edit',
        component: () => import('@/views/service/GuideEditor.vue'),
        meta: {
          title: '事项编辑',
          anon: false
        }
      },
      // 预约管理
      {
        name: 'admin.service.guide.reservation',
        path: '/service/guide/reservation',
        component: () => import('@/views/service/Reservation.vue'),
        meta: {
          title: '预约记录',
          anon: false
        }
      },
      // 反馈管理
      {
        name: 'admin.service.problem.index',
        path: '/service/problem',
        component: () => import('@/views/service/Problem.vue'),
        meta: {
          title: '反馈管理',
          anon: false,
          keepAlive: true
        }
      },
      // 我的
      {
        name: 'admin.me-setting',
        path: '/me/setting',
        component: () => import('@/views/sys/me/Setting.vue'),
        meta: {
          title: '个人中心',
          anon: false,
          keepAlive: true
        }
      }, {
        name: 'admin.me-message',
        path: '/me/message',
        component: () => import('@/views/sys/me/Message.vue'),
        meta: {
          title: '消息中心',
          anon: false,
          keepAlive: true
        }
      },
      // 文件管理
      {
        name: 'admin.media',
        path: '/media',
        component: () => import('@/views/media/Record.vue'),
        meta: {
          title: '文件管理',
          anon: false,
          keepAlive: true
        }
      },
      // 安全
      {
        name: 'admin.sec.online',
        path: '/sec/online',
        component: () => import('@/views/sec/Online.vue'),
        meta: {
          title: '在线管理',
          anon: false,
          keepAlive: true
        }
      },
      // 角色管理
      {
        name: 'admin.sec.role.index',
        path: '/sec/role',
        component: () => import('@/views/sec/Role.vue'),
        meta: {
          title: '角色管理',
          anon: false,
          keepAlive: true
        }
      }, {
        name: 'admin.sec.role.edit',
        path: '/sec/role/edit',
        component: () => import('@/views/sec/RoleEditor.vue'),
        meta: {
          title: '角色编辑',
          anon: false
        }
      }, {
        name: 'admin.sec.role.authorization',
        path: '/sec/role/authorization',
        component: () => import('@/views/sec/RoleAuthorization.vue'),
        meta: {
          title: '角色授权',
          anon: false
        }
      }, {
        name: 'admin.sec.role.appointment',
        path: '/sec/role/appointment',
        component: () => import('@/views/sec/RoleAppointment.vue'),
        meta: {
          title: '人员任命',
          anon: false
        }
      },
      // 活跃统计
      {
        name: 'admin.sys.stat',
        path: '/sys/stat',
        component: () => import('@/views/sys/Stat.vue'),
        meta: {
          title: '活跃统计',
          anon: false,
          keepAlive: true
        }
      },
      // 通讯录
      {
        name: 'admin.sys.contact',
        path: '/sys/contact',
        component: () => import('@/views/sys/Contact.vue'),
        meta: {
          title: '通讯录',
          anon: false,
          keepAlive: true
        }
      },
      // 用户编辑
      {
        name: 'admin.sys.user.edit',
        path: '/sys/user/edit',
        component: () => import('@/views/sys/UserEditor.vue'),
        meta: {
          title: '用户编辑',
          anon: false
        }
      },
      // 作业管理
      {
        name: 'admin.sys.job',
        path: '/sys/job',
        component: () => import('@/views/sys/Jobs.vue'),
        meta: {
          title: '作业管理',
          anon: false,
          keepAlive: true
        }
      },
      // 字典管理
      {
        name: 'admin.sys.dictionary',
        path: '/sys/dictionary',
        component: () => import('@/views/sys/Dictionary.vue'),
        meta: {
          title: '字典管理',
          anon: false,
          keepAlive: true
        }
      }, {
        name: 'admin.sys.dictionary.edit',
        path: '/sys/dictionary/edit',
        component: () => import('@/views/sys/DictionaryEditor.vue'),
        meta: {
          title: '字典项目编辑',
          anon: false
        }
      },
      // 短信管理
      {
        name: 'admin.sys.sms.outbox',
        path: '/sys/sms/outbox',
        component: () => import('@/views/sys/sms/Outbox.vue'),
        meta: {
          title: '发件箱',
          anon: false,
          keepAlive: true
        }
      },
      // 信息日志查询
      {
        name: 'admin.sys.log.info',
        path: '/sys/log/info',
        component: () => import('@/views/sys/log/Info.vue'),
        meta: {
          title: '信息日志',
          anon: false,
          keepAlive: true
        }
      },
      // 错误日志查询
      {
        name: 'admin.sys.log.error',
        path: '/sys/log/error',
        component: () => import('@/views/sys/log/Error.vue'),
        meta: {
          title: '错误日志',
          anon: false,
          keepAlive: true
        }
      },
      // 其它
      {
        path: '/:pathMatch(.*)',
        component: () => import('@/views/exception/404.vue'),
        meta: {
          title: '404',
          anon: true
        }
      }
    ]
  },
  // 使用登录页面布局
  {
    name: 'login',
    path: '/',
    component: () => import('@/layouts/LoginLayout.vue'),
    children: [
      {
        name: 'login.index',
        path: '/sec/login',
        component: () => import('@/views/sec/Login.vue'),
        meta: {
          anon: true
        }
      }
    ],
    beforeEnter: (to, from) => {
      const redirectUrl = to.query && to.query.redirect ? to.query.redirect : from.fullPath

      return Store.state.credential.authenticated
        ? {
            path: redirectUrl
          }
        : true
    }
  }
]

export const router = createRouter({
  history: createWebHashHistory(),
  routes: _routes
})

router.beforeEach((to, from, next) => {
  if (to.meta.anon) { // 支持匿名访问则导航
    next()
  } else if (Store.state.credential.authenticated) { // 已登录则鉴权
    const _promise = Store.state.assets.pages === null
      ? AppApi.getPages({
        showLoading: false,
        toast: {
          success: false
        }
      })
      : Promise.resolve()

    _promise.finally(() => {
      next()
    })
  } else { // 未登录则重定向
    next({
      path: '/sec/login',
      query: {
        redirect: to.fullPath
      }
    })
  }
})

router.afterEach((to, from) => {
})

export const unreliablyUpdateRoutes = pages => {
  const _routes = router.getRoutes()

  // 更新路由
  pages
    .filter(i => typeof i.path === 'string')
    .forEach(i => {
      _routes
        .filter(j => i.path === j.path)
        .forEach(j => {
          j.meta.title = i.title || j.meta.title
          j.meta.anon = i.anon == null ? i.anon : j.meta.anon
        })
    })
}

// 搜索需要缓存的路由
const findKeepAlive = routes => {
  let _array = []

  routes.forEach(value => {
    if (value.meta && value.meta.keepAlive) {
      _array.push(value.name)
    }

    // 递归子路由
    if (Array.isArray(value.children)) {
      _array = _array.concat(findKeepAlive(value.children))
    }
  })

  return _array
}

export const keepAliveComponents = findKeepAlive(_routes)
