import { message, Modal, notification } from 'ant-design-vue'
import { router } from '@/router/index.js'
import Store from '@/store/index.js'
import { extend } from '@/utils/object-util.js'

function FeedbackUtil () {
  this.redirecting = false
}

// 加载中
FeedbackUtil.prototype.loadingBy = function (promise) {
  const that = this

  that.showLoading()

  promise.finally(() => {
    that.hideLoading()
  })
}

// 开启加载
FeedbackUtil.prototype.showLoading = () => {
  Store.commit('showLoading')
}

// 结束加载
FeedbackUtil.prototype.hideLoading = () => {
  Store.commit('hideLoading')
}

FeedbackUtil.prototype.confirmToRedirectToLogin = function (message) {
  const that = this

  // 同时间仅支持一次确认
  if (that.redirecting) {
    return
  }

  that.redirecting = true

  this.modal(message, 'confirm', {
    onOk () {
      Store.commit('logout')

      that.redirectToLogin()
    },
    onCancel () {
      that.redirecting = false
    }
  })
}

FeedbackUtil.prototype.redirectToLogin = function () {
  const that = this

  // 重定向到登录页
  router.push({
    name: 'login.index'
  }).finally(() => {
    that.redirecting = false
  })
}

// 全局提示
FeedbackUtil.prototype.message = (content, type, options) => {
  const _options = {
    content
  }
  extend(true, _options, options)

  switch (type) {
    case 'error':
      return message.error(_options)
    case 'success':
      return message.success(_options)
    case 'warn':
      return message.warning(_options)
    default:
      return notification.info(_options)
  }
}

// 通知
FeedbackUtil.prototype.notification = (content, type, options) => {
  const _options = {
    message: content,
    placement: 'topRight'
  }
  extend(true, _options, options)

  switch (type) {
    case 'error':
      notification.error(_options)
      break
    case 'success':
      notification.success(_options)
      break
    case 'warn':
      notification.warning(_options)
      break
    default:
      notification.info(_options)
      break
  }
}

FeedbackUtil.prototype.modal = (content, type, options) => {
  const _options = {
    title: content,
    centered: true,
    mask: true,
    maskClosable: false,
    keyboard: true,
    okText: '确定',
    cancelText: '取消'
  }
  extend(true, _options, options)

  switch (type) {
    case 'confirm':
      return Modal.confirm(_options)
    case 'error':
      return Modal.error(_options)
    case 'success':
      return Modal.success(_options)
    case 'warn':
      return Modal.warning(_options)
    default:
      return Modal.info(_options)
  }
}

export default new FeedbackUtil()
