const _pattern = {
  chineseName: /^[\u4e00-\u9fa5]{2,4}$/,
  identityNo: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(d|X|x)$)/,
  mp: /^1(3[0-9]|4[01456879]|5[0-3,5-9]|6[2567]|7[0-8]|8[0-9]|9[0-3,5-9])\d{8}$/,
  email: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
}

export const pattern = {
  ..._pattern,
  isChineseName (str) {
    return pattern.chineseName.test(str)
  },
  isIdentityNo (str) {
    return pattern.identityNo.test(str)
  },
  isMp (str) {
    return pattern.mp.test(str)
  },
  isEmail (str) {
    return pattern.email.test(str)
  }
}

export const isEmptyObject = object => {
  // eslint-disable-next-line no-unreachable-loop
  for (const key in object) {
    return false
  }

  return true
}

export const isPlainObject = object => {
  if (!object || Object.prototype.toString.call(object) !== '[object Object]') {
    return false
  }

  const proto = Object.getPrototypeOf(object)
  if (!proto) {
    return true
  }

  const constructor = Object.prototype.hasOwnProperty.call(proto, 'constructor') && proto.constructor
  return typeof constructor === 'function' && Object.prototype.toString.call(constructor) === Object.prototype.toString.call(Object)
}

export const isFunction = object => {
  return typeof object === 'function' && typeof object.nodeType !== 'number' && typeof object.item !== 'function'
}

export const extend = function () {
  let options
  let name
  let src
  let copy
  let copyIsArray
  let clone

  let target = arguments[0] || {}
  let i = 1
  const length = arguments.length
  let deep = false

  // Handle a deep copy situation
  if (typeof target === 'boolean') {
    deep = target

    // Skip the boolean and the target
    target = arguments[i] || {}
    i++
  }

  // Handle case when target is a string or something (possible in deep copy)
  if (typeof target !== 'object' && !isFunction(target)) {
    target = {}
  }

  // Extend jQuery itself if only one argument is passed
  if (i === length) {
    target = this
    i--
  }

  for (; i < length; i++) {
    // Only deal with non-null/undefined values
    if ((options = arguments[i]) != null) {
      // Extend the base object
      for (name in options) {
        copy = options[name]

        // Prevent Object.prototype pollution
        // Prevent never-ending loop
        if (name === '__proto__' || target === copy) {
          continue
        }

        // Recurse if we're merging plain objects or arrays
        if (deep && copy && (isPlainObject(copy) ||
              (copyIsArray = Array.isArray(copy)))) {
          src = target[name]

          // Ensure proper type for the source value
          if (copyIsArray && !Array.isArray(src)) {
            clone = []
          } else if (!copyIsArray && !isPlainObject(src)) {
            clone = {}
          } else {
            clone = src
          }
          copyIsArray = false

          // Never move original objects, clone them
          target[name] = extend(deep, clone, copy)

          // Don't bring in undefined values
        } else if (copy !== undefined) {
          target[name] = copy
        }
      }
    }
  }

  // Return the modified object
  return target
}
