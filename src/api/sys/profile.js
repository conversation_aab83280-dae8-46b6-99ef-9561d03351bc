import CryptoUtil from '@/utils/crypto.js'
import HttpRequest from '@/utils/http.js'
import LoginApi from '@/api/sec/login.js'

export default {
  // 获取个人信息
  get (options) {
    return new HttpRequest().ajax('/sys/profile', null, options)
  },
  // 保存个人信息
  save (user, options) {
    return new HttpRequest().ajax('/sys/profile/save/default', user, options)
  },
  // 修改密码
  changePassword (originalPassword, newPassword, options) {
    return LoginApi.publicKey({
      toast: {
        success: false
      }
    })
      .then(result => {
        return new HttpRequest().ajax('/sys/user/password/change', {
          originalPassword: CryptoUtil.encryptRsa(originalPassword, result.data),
          newPassword: CryptoUtil.encryptRsa(newPassword, result.data)
        }, options)
      })
      .catch(result => {
        return Promise.reject(result)
      })
  }
}
