import CryptoUtil from '@/utils/crypto.js'
import HttpRequest from '@/utils/http.js'
import LoginApi from '@/api/sec/login.js'

export default {
  // 读取详情
  get (id, options) {
    return new HttpRequest().ajax('/sys/user/get', {
      id
    }, options)
  },
  suggest (count, key, options) {
    return new HttpRequest().ajax('/sys/user/suggest', {
      count,
      key
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/sys/user/save/default', record, options)
  },
  // 重置状态
  enable (id, enable, options) {
    return new HttpRequest().ajax(enable ? '/sys/user/enable' : '/sys/user/disable', {
      id
    }, options)
  },
  // 重置密码
  resetPassword (id, password, options) {
    return LoginApi.publicKey({
      toast: {
        success: false
      }
    })
      .then(result => {
        return new HttpRequest().ajax('/sys/user/password/reset', {
          id,
          password: CryptoUtil.encryptRsa(password, result.data)
        }, options)
      })
      .catch(result => {
        return Promise.reject(result)
      })
  }
}
