import HttpRequest from '@/utils/http.js'

export default {
  // 读取活动分类
  subtypes (options) {
    return new HttpRequest().ajax('/cms/activity/subtypes', null, options)
  },
  // 获取详情
  get (id, options) {
    return new HttpRequest().ajax('/cms/activity/get', {
      id
    }, options)
  },
  // 模糊搜索
  search (count, index, subtype, keyword, recommended, registerFrom, registerTo, from, to, status, options) {
    return new HttpRequest().ajax('/cms/activity/search', {
      count,
      index,
      subtype,
      keyword,
      recommended,
      registerFrom,
      registerTo,
      from,
      to,
      status
    }, options)
  },
  // 懒加载
  load (count, index, timestamp, subtype, keyword, recommended, options) {
    return new HttpRequest().ajax('/cms/activity/load', {
      count,
      index,
      timestamp,
      subtype,
      keyword,
      recommended
    }, options)
  },
  // 保存，保存record对象
  save (record, options) {
    return new HttpRequest().ajax('/cms/activity/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/cms/activity/remove', { id }, options)
  },
  // 审核
  audit (id, status, options) {
    return new HttpRequest().ajax('/cms/activity/audit', { id, status }, options)
  },
  // 取消发布
  retract (id, options) {
    return new HttpRequest().ajax('/cms/activity/retract', { id }, options)
  },
  // 归档
  archive (id, options) {
    return new HttpRequest().ajax('/cms/activity/archive', {
      id
    }, options)
  },
  // 模糊搜索报名
  searchParticipants (count, index, activityId, name, options) {
    return new HttpRequest().ajax('/cms/activity/participant/search', {
      count,
      index,
      id: activityId,
      name
    }, options)
  }
}
