import HttpRequest from '@/utils/http.js'

export default {
  // 查询
  find (options) {
    return new HttpRequest().ajax('/cms/banner/slider/find', null, options)
  },
  articles (count, index, keyword, options) {
    return new HttpRequest().ajax('/cms/banner/slider/candidate/article', {
      count,
      index,
      keyword
    }, options)
  },
  activities (count, index, keyword, options) {
    return new HttpRequest().ajax('/cms/banner/slider/candidate/activity', {
      count,
      index,
      keyword
    }, options)
  },
  // 保存
  save (slides, options) {
    return new HttpRequest().ajax('/cms/banner/slider/save', {
      slides
    }, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/cms/banner/slider/remove', {
      id
    }, options)
  }
}
