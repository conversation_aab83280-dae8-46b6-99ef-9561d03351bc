import HttpRequest from '@/utils/http.js'

export default {
  // 读取文章分类
  subtypes (options) {
    return new HttpRequest().ajax('/cms/article/subtypes', null, options)
  },
  // 模糊搜索
  search (count, index, subtype, keyword, recommended, from, to, status, options) {
    return new HttpRequest().ajax('/cms/article/search', {
      count,
      index,
      subtype,
      keyword,
      recommended,
      from,
      to,
      status
    }, options)
  },
  // 懒加载
  load (count, index, timestamp, subtype, keyword, recommended, from, to, options) {
    return new HttpRequest().ajax('/cms/article/load', {
      count,
      index,
      timestamp,
      subtype,
      keyword,
      recommended,
      from,
      to
    }, options)
  },
  // 获取详情
  get (id, options) {
    return new HttpRequest().ajax('/cms/article/get', {
      id
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/cms/article/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/cms/article/remove', {
      id
    }, options)
  },
  // 审核
  audit (id, status, options) {
    return new HttpRequest().ajax('/cms/article/audit', {
      id,
      status
    }, options)
  },
  // 取消发布
  retract (id, options) {
    return new HttpRequest().ajax('/cms/article/retract', {
      id
    }, options)
  }
}
