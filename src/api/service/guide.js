import HttpRequest from '@/utils/http.js'

export default {
  // 读取事项分类
  subtypes (options) {
    return new HttpRequest().ajax('/service/guide/subtypes', null, options)
  },
  // 模糊搜索
  search (count, index, subtype, keyword, from, to, status, options) {
    return new HttpRequest().ajax('/service/guide/search', {
      count,
      index,
      subtype,
      keyword,
      from,
      to,
      status
    }, options)
  },
  suggest (options) {
    return new HttpRequest().ajax('/service/guide/suggest', null, options)
  },
  // 获取详情
  get (id, options) {
    return new HttpRequest().ajax('/service/guide/get', {
      id
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/service/guide/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/service/guide/remove', {
      id
    }, options)
  },
  // 审核
  audit (id, status, options) {
    return new HttpRequest().ajax('/service/guide/audit', {
      id,
      status
    }, options)
  },
  // 取消发布
  retract (id, options) {
    return new HttpRequest().ajax('/service/guide/retract', {
      id
    }, options)
  },
  // 预约详情
  getReservation (id, options) {
    return new HttpRequest().ajax('/service/reservation/get', {
      id
    }, options)
  },
  // 模糊搜索预约
  searchReservations (count, index, guideId, name, contact, from, to, done, options) {
    return new HttpRequest().ajax('/service/reservation/search', {
      count,
      index,
      guideId,
      name,
      contact,
      from,
      to,
      done
    }, options)
  },
  // 办理
  transactReservation (id, options) {
    return new HttpRequest().ajax('/service/reservation/transact', {
      id
    }, options)
  }
}
