import HttpRequest from '@/utils/http.js'

export default {
  // 获取详情
  get (id, options) {
    return new HttpRequest().ajax('/service/problem/get', {
      id
    }, options)
  },
  // 模糊搜索
  search (count, index, urgency, keyword, from, to, done, options) {
    return new HttpRequest().ajax('/service/problem/search', {
      count,
      index,
      urgency,
      keyword,
      from,
      to,
      done
    }, options)
  },
  // 办理
  transact (id, options) {
    return new HttpRequest().ajax('/service/problem/transact', {
      id
    }, options)
  }
}
