import HttpRequest from '@/utils/http.js'
import { extend } from '@/utils/object-util.js'

export default {
  // 获取校验规则
  validation (className, props, options) {
    const _options = {
      showLoading: false,
      toast: {
        success: false
      }
    }

    extend(true, _options, options)

    const _promise = new HttpRequest().ajax('/util/validation/jquery', {
      class: className
    }, _options)

    const _rules = {}
    props.forEach(i => {
      _rules[i] = new Promise(resolve => {
        _promise
          .then(result => {
            const _rules = []

            const temp = result.data[i]

            if (temp.rule) {
              // 必填
              if (temp.rule.required === true) {
                _rules.push({
                  required: true,
                  whitespace: true,
                  message: '请输入'
                })
              }

              // 长度
              if (Number.isFinite(temp.rule.maxlength)) {
                _rules.push({
                  max: temp.rule.maxlength,
                  message: `最多可以输入${temp.rule.maxlength}个字符，请修改`
                })
              }

              // 数字类型
              if (temp.rule.digits === true) {
                _rules.push({
                  type: 'integer',
                  message: '请输入有效的整数'
                })
              }

              if (temp.rule.number === true) {
                _rules.push({
                  type: 'number',
                  message: '请输入有效的数字'
                })
              }

              if (temp.rule.date === true) {
                _rules.push({
                  type: 'date',
                  message: '请输入有效的日期'
                })
              }
            }

            const _config = {}
            if (_rules.length > 0) {
              _config.rules = _rules
            }

            if (Array.isArray(temp.options)) {
              _config.options = temp.options.map(i => {
                return {
                  label: i.value,
                  value: i.key
                }
              })
            }

            resolve(_config)
          })
          .catch(() => resolve(null))
      })
    })

    return _rules
  },
  aMapJsKey (options) {
    return new HttpRequest().ajax('/util/amap/js-key', null, options)
  },
  qqMapJsKey (options) {
    return new HttpRequest().ajax('/util/qqmap/js-key', null, options)
  }
}
