import Store from '@/store/index.js'
import HttpRequest from '@/utils/http.js'

export default {
  // 读取权限
  find (options) {
    return new HttpRequest().ajax('/sec/permission/find', null, options)
  },
  // 我的权限
  me (options) {
    if (Store.state.assets.permissions === null) {
      return new HttpRequest().ajax('/sec/permission/me', null, options).then(result => {
        // 替换通配符
        result.data.filter(i => i.name)
          .forEach(i => {
            i.name = i.name.replaceAll('*', '.')
          })

        Store.commit('setPermissions', result.data)

        return Promise.resolve({
          code: 'OK',
          data: Store.state.assets.permissions
        })
      })
    } else {
      return Promise.resolve({
        code: 'OK',
        data: Store.state.assets.permissions
      })
    }
  }
}
