import HttpRequest from '@/utils/http.js'

export default {
  // 读取详情
  get (id, options) {
    return new HttpRequest().ajax('/sec/role/get', {
      id
    }, options)
  },
  // 模糊搜索
  search (count, index, sortBy, name, options) {
    return new HttpRequest().ajax('/sec/role/search', {
      count,
      index,
      sortBy,
      name
    }, options)
  },
  // 我的角色
  me (options) {
    return new HttpRequest().ajax('/sec/role/me', null, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/sec/role/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/sec/role/remove', {
      id
    }, options)
  },
  // 读取权限
  findPermissions (id, options) {
    return new HttpRequest().ajax('/sec/role/permission/find', {
      id
    }, options)
  },
  // 保存权限
  savePermission (id, permissionIds, options) {
    return new HttpRequest().ajax('/sec/role/permission/save', {
      id,
      permissionIds
    }, options)
  },
  // 模糊搜索用户
  findUser (count, index, sortBy, id, account, username, mp, options) {
    return new HttpRequest().ajax('/sec/role/user/find', {
      count,
      index,
      sortBy,
      id,
      account,
      username,
      mp
    }, options)
  },
  // 任命
  addUser (id, userId, options) {
    return new HttpRequest().ajax('/sec/role/user/add', {
      id,
      userId
    }, options)
  },
  // 解除任命
  removeUser (id, userId, options) {
    return new HttpRequest().ajax('/sec/role/user/remove', {
      id,
      userId
    }, options)
  }
}
