<script setup>
import { nextTick, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import GenericForm from '@/components/GenericForm.vue'
import SearchBar from '@/components/SearchBar.vue'
import StatefulModal from '@/components/StatefulModal.vue'
import MediaApi from '@/api/media/record.js'
import ProblemApi from '@/api/service/problem.js'

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '关键字',
    field: 'keyword',
    icon: 'FormOutlined',
    type: 'text'
  }, {
    title: '紧急程度',
    field: 'urgency',
    type: 'select',
    config: {
      options: [{
        label: '普通',
        value: 0
      }, {
        label: '紧急',
        value: 1
      }]
    }
  }, {
    title: '时间',
    field: 'range',
    type: 'range',
    config: {
      placeholder: ['开始日期', '结束日期']
    }
  }, {
    title: '是否已办理',
    field: 'done',
    type: 'select',
    value: false,
    config: {
      options: [{
        label: '是',
        value: true
      }, {
        label: '否',
        value: false
      }]
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/service/problem',
    idField: 'id'
  },
  columns: [{
    title: '内容',
    dataIndex: 'body'
  }, {
    title: '紧急程度',
    dataIndex: 'urgency',
    type: 'select',
    config: {
      options: [{
        label: '普通',
        value: 0
      }, {
        label: '紧急',
        value: 1
      }]
    }
  }, {
    title: '名称',
    dataIndex: 'name'
  }, {
    title: '联系方式',
    dataIndex: 'contact'
  }, {
    title: '是否已处理',
    dataIndex: 'done',
    type: 'select',
    config: {
      options: [{
        label: '是',
        value: true
      }, {
        label: '否',
        value: false
      }]
    }
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime'
  }],
  actions: [{
    title: '详情',
    icon: 'AuditOutlined',
    callback (record) {
      detail.value.show(record)
    }
  }]
}

const load = (count, index) => {
  const _filters = searchbar.value.model()
  let _from = null
  let _to = null
  if (Array.isArray(_filters.range) && _filters.range.length > 0) {
    _from = _filters.range[0].format('YYYY-MM-DD')
    _to = _filters.range.length > 1 ? _filters.range[1].format('YYYY-MM-DD') : null
  }

  return new Promise(resolve => {
    ProblemApi.search(count, index, _filters.urgency, _filters.keyword, _from, _to, _filters.done, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const modal = ref()
const form = ref()

const detail = ref({
  fields: [{
    title: '内容',
    field: 'body',
    config: {
      disabled: true
    }
  }, {
    title: '紧急程度',
    field: 'urgency',
    type: 'select',
    config: {
      disabled: true,
      options: [{
        label: '普通',
        value: 0
      }, {
        label: '紧急',
        value: 1
      }]
    }
  }, {
    title: '名称',
    field: 'name',
    config: {
      disabled: true
    }
  }, {
    title: '联系方式',
    field: 'contact',
    config: {
      disabled: true
    }
  }, {
    title: '附件',
    field: '_attachments',
    type: 'file',
    config: {
      disabled: true,
      showUploadList: {
        showPreviewIcon: false,
        showRemoveIcon: false,
        showDownloadIcon: true
      }
    }
  }, {
    title: '是否已处理',
    field: 'done',
    type: 'select',
    config: {
      disabled: true,
      options: [{
        label: '是',
        value: true
      }, {
        label: '否',
        value: false
      }]
    }
  }],
  buttons: [],
  model: {
    id: null
  },
  show (model) {
    detail.value.buttons = model.done === '是'
      ? []
      : [{
          title: '办理',
          onClick () {
            return ProblemApi.transact(detail.value.model.id, {
              showLoading: false
            }).then(() => {
              // 关闭窗口
              detail.value.close()

              // 刷新数据
              table.value.load()
            })
          }
        }]

    console.log(detail.value.buttons)

    detail.value.model = {
      ...model
    }

    // 转换附件
    detail.value.model._attachments = model.attachments
      ? {
          ids: (model.attachments || []).map(i => i.id),
          files: (model.attachments || []).map(i => {
            return {
              id: i.id,
              name: i.name,
              url: MediaApi.preview(i.id)
            }
          })
        }
      : null

    modal.value.show()

    nextTick(() => {
      form.value.setModel(detail.value.model)
    })
  },
  close () {
    modal.value.close()
  }
})

</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>

  <StatefulModal
    ref="modal"
    :buttons="detail.buttons"
    :width="'calc(100vw * 0.8)'"
  >
    <GenericForm
      ref="form"
      :fields="detail.fields"
    />
  </StatefulModal>
</template>
