<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import EdiTable from '@/components/EdiTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import GuideApi from '@/api/service/guide.js'
import MediaApi from '@/api/media/record.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = ref({
  fields: [{
    title: '类型',
    field: 'subtype',
    type: 'select'
  }, {
    title: '关键字',
    field: 'keyword',
    icon: 'FormOutlined',
    type: 'text'
  }, {
    title: '时间',
    field: 'range',
    type: 'range',
    config: {
      placeholder: ['开始日期', '结束日期']
    }
  }, {
    title: '状态',
    field: 'status',
    type: 'select',
    config: {
      options: [{
        label: '草稿',
        value: 'DRAFT'
      }, {
        label: '待审核',
        value: 'EDIT'
      }, {
        label: '已发布',
        value: 'PUBLISHED'
      }]
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
})

const table = ref()
const tableOptions = {
  mapper: {
    path: '/service/guide',
    idField: 'id'
  },
  columns: [{
    title: '封面',
    dataIndex: '_cover',
    type: 'image'
  }, {
    title: '事项',
    dataIndex: 'title'
  }, {
    title: '类型',
    dataIndex: 'subtype'
  }, {
    title: '状态',
    dataIndex: 'status',
    type: 'select',
    config: {
      options: [{
        label: '草稿',
        value: 'DRAFT'
      }, {
        label: '待审核',
        value: 'EDIT'
      }, {
        label: '已发布',
        value: 'PUBLISHED'
      }]
    }
  }, {
    title: '作者',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime'
  }],
  actions: [{
    title: '预约记录',
    icon: 'SolutionOutlined',
    callback (record) {
      proxy.$router.push({
        name: 'admin.service.guide.reservation',
        query: {
          guideId: record.id
        }
      })

      return Promise.resolve()
    }
  }]
}

const load = (count, index) => {
  const _filters = searchbar.value.model()
  let _from = null
  let _to = null
  if (Array.isArray(_filters.range) && _filters.range.length > 0) {
    _from = _filters.range[0].format('YYYY-MM-DD')
    _to = _filters.range.length > 1 ? _filters.range[1].format('YYYY-MM-DD') : null
  }

  return new Promise(resolve => {
    GuideApi.search(count, index, _filters.subtype, _filters.keyword, _from, _to, _filters.status ? [_filters.status] : null, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      result.data.records.forEach(i => {
        i._cover = Array.isArray(i.coverIds) && i.coverIds.length > 0 ? MediaApi.preview(i.coverIds[0]) : null
      })

      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const remove = (record) => {
  return GuideApi.remove(record.id)
}

const ready = ref(false)

onMounted(() => {
  GuideApi.subtypes({
    loading: false,
    toast: {
      success: false
    }
  }).then(result => {
    searchbarOptions.value.fields[0].config = {
      options: result.data.map(i => {
        return {
          label: i,
          value: i
        }
      })
    }

    ready.value = true
  })
})
</script>

<template>
  <template v-if="ready">
    <div class="layout-content-panel">
      <SearchBar
        ref="searchbar"
        :fields="searchbarOptions.fields"
        :actions="searchbarOptions.actions"
      />
    </div>

    <div class="layout-content-panel">
      <EdiTable
        ref="table"
        :mapper="tableOptions.mapper"
        :actions="tableOptions.actions"
        :columns="tableOptions.columns"
        :addable="true"
        :load="load"
        :remove="remove"
      />
    </div>
  </template>
</template>
