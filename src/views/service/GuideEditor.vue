<script setup>
import { getCurrentInstance } from 'vue'
import Editor from '@/components/ArticleEditor.vue'
import GuideApi from '@/api/service/guide.js'
import MediaApi from '@/api/media/record.js'

const { proxy } = getCurrentInstance()

// 公共字段
const fields = [{
  title: '标题',
  field: 'title',
  type: 'text',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}]

// 图文字段
const richTextFields = [{
  title: '封面',
  field: '_covers',
  type: 'file',
  config: {
    component: 'gallery',
    label: null,
    max: 1,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false
    },
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
},
...fields, {
  title: '类型',
  field: 'subtype',
  type: 'select',
  config: {
    options: [{
      label: '镇/街道',
      value: '镇/街道'
    }, {
      label: '村/社区',
      value: '村/社区'
    }],
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '正文',
  field: 'body',
  type: 'text',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '办理地址',
  field: '_location',
  type: 'location',
  config: {
    disabled: false
  }
}, {
  title: '附件',
  field: '_attachments',
  type: 'file',
  config: {
    max: Number.MAX_VALUE,
    showUploadList: {
      showPreviewIcon: false,
      showRemoveIcon: true,
      showDownloadIcon: true
    }
  }
}]

// 粤省事字段
const yueshengshiFields = [
  ...fields, {
    title: '小程序id',
    field: 'appId',
    type: 'text',
    config: {
      rules: [{
        required: true,
        message: '此内容必须输入'
      }]
    }
  }, {
    title: '小程序链接',
    field: 'url',
    type: 'text',
    config: {
      rules: [{
        required: true,
        message: '此内容必须输入'
      }]
    }
  }]

const convert = record => {
  const _record = {
    ...record
  }
  delete _record._location
  delete _record._covers
  delete _record._attachments

  if (['镇/街道', '村/社区'].indexOf(_record.subtype) !== -1) {
    if (record._location === null) {
      _record.address = null
      _record.lng = null
      _record.lat = null
    } else {
      _record.address = record._location.address
      _record.lng = record._location.location.lng
      _record.lat = record._location.location.lat
    }

    _record.coverIds = record._covers === null ? null : record._covers.ids
    _record.attachmentIds = record._attachments === null ? null : record._attachments.ids
  } else {
    _record.subtype = '粤省事'
  }

  return _record
}

const read = id => {
  return GuideApi.get(id, {
    loading: true,
    toast: {
      success: false
    }
  })
}

const updateModelAfterRead = record => {
  const _record = {
    ...record
  }

  // 转换位置
  _record._location = {
    address: record.address,
    location: {
      lng: record.lng,
      lat: record.lat
    }
  }

  // 转换封面
  _record._covers = _record.coverIds
    ? {
        ids: _record.coverIds || [],
        files: (_record.coverIds || []).map(i => {
          return {
            id: i,
            name: '',
            url: MediaApi.preview(i)
          }
        })
      }
    : null

  // 转换附件
  _record._attachments = _record.attachments
    ? {
        ids: (_record.attachments || []).map(i => i.id),
        files: (_record.attachments || []).map(i => {
          return {
            id: i.id,
            name: i.name,
            url: MediaApi.preview(i.id)
          }
        })
      }
    : null

  return _record
}

const save = record => {
  return GuideApi.save(convert(record))
}

const remove = record => {
  const _promise = GuideApi.remove(record.id)
  _promise.then(() => {
    proxy.$router.replace({
      name: 'admin.service.guide.index'
    })
  })

  return _promise
}

const submit = record => {
  const _record = convert(record)
  _record.status = 'EDIT'

  return GuideApi.save(_record)
}

const audit = {
  permission: 'service:guide:audit',
  callback (record, status) {
    return GuideApi.audit(record.id, status)
  }
}

const retract = {
  permission: 'service:guide:retract',
  callback (record) {
    return GuideApi.retract(record.id)
  }
}
</script>

<template>
  <div class="layout-content-panel">
    <Editor
      :theme="{
        type: 'guide',
        tabs: [{
          key: 1,
          title: '本地'
        }, {
          key: 2,
          title: '粤省事'
        }]
      }"
      :rich-text-fields="richTextFields"
      :channel-fields="yueshengshiFields"
      :read="read"
      :update-model-after-read="updateModelAfterRead"
      :save="save"
      :submit="submit"
      :remove="remove"
      :audit="audit"
      :retract="retract"
    />
  </div>
</template>
