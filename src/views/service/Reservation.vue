<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import GuideApi from '@/api/service/guide.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()

const searchbarOptions = {
  fields: [{
    title: '姓名',
    field: 'name',
    icon: 'UserOutlined',
    type: 'text'
  }, {
    title: '联系方式',
    field: 'contact',
    icon: 'MobileOutlined',
    type: 'text'
  }, {
    title: '时间',
    field: 'range',
    type: 'range',
    config: {
      placeholder: ['开始日期', '结束日期']
    }
  }, {
    title: '是否已办理',
    field: 'done',
    type: 'select',
    value: false,
    config: {
      options: [{
        label: '是',
        value: true
      }, {
        label: '否',
        value: false
      }]
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/service/reservation/',
    idField: 'id'
  },
  columns: [{
    title: '姓名',
    dataIndex: 'name'
  }, {
    title: '联系方式',
    dataIndex: 'contact'
  }, {
    title: '是否已办理',
    dataIndex: 'done',
    type: 'select',
    config: {
      options: [{
        label: '是',
        value: true
      }, {
        label: '否',
        value: false
      }]
    }
  }, {
    title: '预约时间',
    dataIndex: 'time'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime'
  }],
  actions: [{
    title: '办理',
    icon: 'AuditOutlined',
    callback (record) {
      if (record.id != null) {
        GuideApi.transactReservation(record.id).then(() => {
          table.value.load()
        })
      }
    }
  }]
}

const load = (count, index) => {
  const _filters = searchbar.value.model()
  let _from = null
  let _to = null
  if (Array.isArray(_filters.range) && _filters.range.length > 0) {
    _from = _filters.range[0].format('YYYY-MM-DD')
    _to = _filters.range.length > 1 ? _filters.range[1].format('YYYY-MM-DD') : null
  }

  return new Promise(resolve => {
    GuideApi.searchReservations(count, index, proxy.$route.query.guideId || _filters.guideId, _filters.name, _filters.contact, _from, _to, _filters.done, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      result.data.records.forEach(i => {
        i._actions_ = i.done ? [] : [0]
      })

      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const ready = ref(false)

const guide = ref(null)

onMounted(() => {
  if (proxy.$route.query.guideId) {
    GuideApi.get(proxy.$route.query.guideId, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      guide.value = result.data

      ready.value = true
    })
  } else {
    GuideApi.suggest({
      showLoading: true,
      toast: {
        success: false
      }
    }).then(result => {
      searchbarOptions.fields.splice(0, 0, {
        title: '办理事项',
        field: 'guideId',
        type: 'select',
        config: {
          options: result.data.map(i => {
            return {
              label: i.two,
              value: i.one
            }
          })
        }
      })

      ready.value = true
    })
  }
})
</script>

<template>
  <template v-if="ready">
    <template v-if="guide !== null">
      <teleport to=".ant-tabs-tabpane-active">
        <a-page-header
          :title="guide.title || '-'"
          @back="() => $router.go(-1)"
        >
          <a-divider />
          <a-descriptions
            :column="{ xs: 1, md: 3, xl: 6 }"
            :size="'small'"
          >
            <a-descriptions-item :label="'类型'">
              {{ guide.subtype || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-page-header>
      </teleport>
    </template>

    <div class="layout-content-panel">
      <SearchBar
        ref="searchbar"
        :fields="searchbarOptions.fields"
        :actions="searchbarOptions.actions"
      />
    </div>

    <div class="layout-content-panel">
      <DataTable
        ref="table"
        :actions="tableOptions.actions"
        :columns="tableOptions.columns"
        :load="load"
      />
    </div>
  </template>
</template>
