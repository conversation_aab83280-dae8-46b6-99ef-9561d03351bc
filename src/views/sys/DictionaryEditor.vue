<script setup>
import { getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import DictionaryApi from '@/api/sys/dictionary.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.domain.model.sys.Dictionary', ['groupId', 'name', 'val', 'seq', 'buildIn', 'readonly', 'description', 'isEnabled'])

const fields = [{
  title: '分组',
  field: 'groupId',
  type: 'text',
  config: {
    promise: _config.groupId
  }
}, {
  title: '名称',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '值',
  field: 'val',
  type: 'text',
  config: {
    promise: _config.val
  }
}, {
  title: '排序',
  field: 'seq',
  type: 'number',
  config: {
    promise: _config.seq
  }
}, {
  title: '内置变量',
  field: 'buildIn',
  type: 'select',
  config: {
    disabled: true,
    options: [{
      label: '是',
      value: true
    }, {
      label: '否',
      value: false
    }],
    promise: _config.buildIn
  }
}, {
  title: '只读变量',
  field: 'readonly',
  type: 'select',
  config: {
    disabled: true,
    options: [{
      label: '是',
      value: true
    }, {
      label: '否',
      value: false
    }],
    promise: _config.readonly
  }
}, {
  title: '备注',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '是否启用',
  field: 'isEnabled',
  type: '_switch',
  config: {
    promise: _config.isEnabled
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

const reloadPage = inject('reloadPage')

const actions = ref([{
  callback (record) {
    const _promise = DictionaryApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DictionaryApi.remove(record.id)

    _promise.then(() => {
      proxy.$router.replace({
        name: 'admin.sys.dictionary'
      })
    })

    return _promise
  }
}])

const form = ref()

onMounted(() => {
  if (proxy.$route.query.id) {
    DictionaryApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单
        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      isEnabled: true
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>
</template>
