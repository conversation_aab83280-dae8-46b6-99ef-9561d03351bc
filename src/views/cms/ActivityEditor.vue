<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import Editor from '@/components/ArticleEditor.vue'
import ActivityApi from '@/api/cms/activity.js'
import MediaApi from '@/api/media/record.js'

const { proxy } = getCurrentInstance()

const fields = ref([{
  title: '标题',
  field: 'title',
  type: 'text',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '封面',
  field: '_covers',
  type: 'file',
  config: {
    component: 'gallery',
    label: null,
    max: 1,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false
    },
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '类型',
  field: 'subtype',
  type: 'select',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '活动内容',
  field: 'body',
  type: 'text',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '活动地址',
  field: '_location',
  type: 'location',
  config: {
    disabled: false
  }
}, {
  title: '报名时间',
  field: '_registerTimes',
  type: 'datetime',
  config: {
    range: true,
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '活动时间',
  field: '_times',
  type: 'datetime',
  config: {
    range: true,
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '附件',
  field: '_attachments',
  type: 'file',
  config: {
    max: Number.MAX_VALUE,
    showUploadList: {
      showPreviewIcon: false,
      showRemoveIcon: true,
      showDownloadIcon: true
    }
  }
}])

const convert = record => {
  const _record = {
    ...record
  }
  delete _record._registerTimes
  delete _record._times
  delete _record._location
  delete _record._covers
  delete _record._attachments

  _record.registerFrom = record._registerTimes[0]
  _record.registerTo = record._registerTimes[1]
  _record.from = record._times[0]
  _record.to = record._times[1]

  if (record._location === null) {
    _record.address = null
    _record.lng = null
    _record.lat = null
  } else {
    _record.address = record._location.address
    _record.lng = record._location.location.lng
    _record.lat = record._location.location.lat
  }

  _record.coverIds = record._covers === null ? null : record._covers.ids
  _record.attachmentIds = record._attachments === null ? null : record._attachments.ids

  return _record
}

const read = id => {
  return ActivityApi.get(id, {
    loading: true,
    toast: {
      success: false
    }
  })
}

const updateModelAfterRead = record => {
  const _record = {
    ...record
  }

  _record._registerTimes = [record.registerFrom, record.registerTo]
  _record._times = [record.from, record.to]

  // 转换位置
  _record._location = {
    address: record.address,
    location: {
      lng: record.lng,
      lat: record.lat
    }
  }

  // 转换封面
  _record._covers = _record.coverIds
    ? {
        ids: _record.coverIds || [],
        files: (_record.coverIds || []).map(i => {
          return {
            id: i,
            name: '',
            url: MediaApi.preview(i)
          }
        })
      }
    : null

  // 转换附件
  _record._attachments = _record.attachments
    ? {
        ids: (_record.attachments || []).map(i => i.id),
        files: (_record.attachments || []).map(i => {
          return {
            id: i.id,
            name: i.name,
            url: MediaApi.preview(i.id)
          }
        })
      }
    : null

  return _record
}

const save = record => {
  return ActivityApi.save(convert(record))
}

const remove = record => {
  const _promise = ActivityApi.remove(record.id)
  _promise.then(() => {
    proxy.$router.replace({
      name: 'admin.cms.activity.index'
    })
  })

  return _promise
}

const submit = record => {
  const _record = convert(record)
  _record.status = 'EDIT'

  return ActivityApi.save(_record)
}

const audit = {
  permission: 'cms:activity:audit',
  callback (record, status) {
    return ActivityApi.audit(record.id, status)
  }
}

const retract = {
  permission: 'cms:activity:retract',
  callback (record) {
    return ActivityApi.retract(record.id)
  }
}

const archive = {
  permission: 'cms:activity:archive',
  callback (record) {
    return ActivityApi.archive(record.id)
  }
}

const ready = ref(false)

onMounted(() => {
  // 读取活动类型
  ActivityApi.subtypes({
    loading: true,
    toast: {
      success: false
    }
  })
    .then(result => {
      const _options = result.data.map(i => {
        return {
          label: i.val,
          value: i.id
        }
      })

      fields.value[2].config.options = _options

      ready.value = true
    })
})
</script>

<template>
  <div class="layout-content-panel">
    <template v-if="ready">
      <Editor
        :rich-text-fields="fields"
        :read="read"
        :update-model-after-read="updateModelAfterRead"
        :save="save"
        :submit="submit"
        :remove="remove"
        :audit="audit"
        :retract="retract"
        :archive="archive"
      />
    </template>
  </div>
</template>
