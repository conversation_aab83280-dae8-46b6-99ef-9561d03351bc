<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import ActivityApi from '@/api/cms/activity.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = ref({
  fields: [{
    title: '姓名',
    field: 'name',
    icon: 'UserOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
})

const table = ref()
const tableOptions = {
  columns: [{
    title: '姓名',
    dataIndex: 'name'
  }, {
    title: '联系方式',
    dataIndex: 'mp'
  }, {
    title: '人数',
    dataIndex: 'enrollment'
  }, {
    title: '报名时间',
    dataIndex: 'createTime'
  }]
}

const load = (count, index) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    ActivityApi.searchParticipants(count, index, proxy.$route.query.activityId, _filters.name, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const ready = ref(false)

const activity = ref({})

onMounted(() => {
  ready.value = true

  ActivityApi.get(proxy.$route.query.activityId, {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }).then(result => {
    activity.value = result.data
  })
})
</script>

<script>
export default {
  beforeRouteEnter (to, from, next) {
    if (Object.prototype.hasOwnProperty.call(to, 'query') && Object.prototype.hasOwnProperty.call(to.query, 'activityId')) {
      next()
    } else {
      next({
        name: 'admin.cms.activity.index'
      })
    }
  }
}
</script>

<template>
  <template v-if="ready">
    <teleport to=".ant-tabs-tabpane-active">
      <a-page-header
        :title="activity.title || '-'"
        @back="() => $router.go(-1)"
      >
        <a-divider />
        <a-descriptions
          :column="{ xs: 1, md: 4 }"
          :size="'small'"
        >
          <a-descriptions-item :label="'活动内容'">
            {{ activity.body || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建人员'">
            {{ activity.creatorName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建时间'">
            {{ activity.createTime || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions
          :column="{ xs: 1, md: 4 }"
          :size="'small'"
        >
          <a-descriptions-item :label="'报名开始时间'">
            {{ activity.registerFrom || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'报名截止时间'">
            {{ activity.registerTo || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'活动开始时间'">
            {{ activity.from || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'活动结束时间'">
            {{ activity.to || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-page-header>
    </teleport>
  </template>

  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>
</template>
