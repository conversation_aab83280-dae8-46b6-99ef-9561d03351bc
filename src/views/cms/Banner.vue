<script setup>
import { onMounted, ref } from 'vue'
import ArticlePicker from '@/views/cms/ArticlePicker.vue'
import BannerApi from '@/api/cms/banner.js'
import MediaApi from '@/api/media/record.js'

const slides = ref([])

const load = () => {
  BannerApi.find({
    showLoading: true,
    toast: {
      success: false
    }
  }).then(result => {
    slides.value = result.data.map(i => {
      const _temp = {
        articleId: i.articleId,
        type: i.type
      }

      if (i.article !== null) {
        _temp.title = i.article.title
        _temp.cover = Array.isArray(i.article.coverIds) && i.article.coverIds.length > 0 ? MediaApi.preview(i.article.coverIds[0]) : null
        _temp.body = i.article.body
        _temp.digest = i.article.digest
      }

      return _temp
    })
  })
}

const save = () => {
  const _slides = []

  for (let i = 0; i < slides.value.length; i++) {
    _slides.push({
      articleId: slides.value[i].articleId,
      type: slides.value[i].type,
      seq: i
    })
  }

  return BannerApi.save(_slides, {
    showLoading: false,
    toast: {
      success: false
    }
  })
}

const move = (index, direction) => {
  // 交换位置
  const _temp = slides.value[index]
  slides.value.splice(index, 1)
  slides.value.splice(index + direction, 0, _temp)

  // 保存
  save()
}

const remove = index => {
  // 删除
  slides.value.splice(index, 1)

  // 保存
  save()
}

const picker = ref()
const onOk = items => {
  items.forEach(i => {
    slides.value.push(i)
  })

  // 保存
  save()

  return Promise.resolve()
}

onMounted(() => {
  load()
})
</script>

<template>
  <div class="layout-content-panel">
    <a-list
      :data-source="slides"
      :item-layout="'vertical'"
    >
      <a-list-item>
        <a-button
          :type="'dashed'"
          :block="true"
          @click="picker.show()"
        >
          新增
        </a-button>
      </a-list-item>

      <template #renderItem="{ item, index }">
        <a-list-item>
          <template #actions>
            <template v-if="index > 0">
              <a-button
                :shape="'circle'"
                @click="move(index, -1)"
              >
                <arrow-up-outlined />
              </a-button>
            </template>

            <template v-if="index < slides.length - 1">
              <a-button
                :shape="'circle'"
                @click="move(index, 1)"
              >
                <template #icon>
                  <arrow-down-outlined />
                </template>
              </a-button>
            </template>

            <a-button
              :danger="true"
              :shape="'circle'"
              @click="remove(index)"
            >
              <template #icon>
                <close-outlined />
              </template>
            </a-button>
          </template>

          <a-list-item-meta>
            <template #avatar>
              <img
                :src="item.cover"
                alt=""
              >
            </template>

            <template #title>
              <a>{{ item.title }}</a>
            </template>

            <template #description>
              <a-typography-paragraph
                :ellipsis="{
                  rows: 1,
                  expandable: true
                }"
                :content="item.digest"
              />
            </template>
          </a-list-item-meta>
        </a-list-item>
      </template>
    </a-list>
  </div>

  <ArticlePicker
    ref="picker"
    :on-ok="slides => onOk(slides)"
  />
</template>

<style lang="less">
.ant-list-item {
  .ant-list-item-action {
    margin-top: 0 !important;
  }

  .ant-list-item-meta-title {
    margin-block-end: 0!important;
  }
}
</style>

<style lang="less" scoped>
.ant-list-item {
  display: flex;
  align-items: center;

  .ant-list-item-meta {
    margin-bottom: 0 !important;

    .ant-list-item-meta-avatar {
      display: flex;

      img {
        height: 79px;
      }
    }
  }
}
</style>
