<script setup>
import { nextTick, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import StatefulModal from '@/components/StatefulModal.vue'
import BannerApi from '@/api/cms/banner.js'
import MediaApi from '@/api/media/record.js'

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop
  onOk: {
    type: Function
  }
})

const searchbar = ref()
const searchbarOptions = ref({
  fields: [{
    title: '类型',
    field: 'type',
    type: 'select',
    value: '文章',
    config: {
      options: [{
        label: '文章',
        value: '文章'
      }, {
        label: '活动',
        value: '活动'
      }]
    }
  }, {
    title: '标题',
    field: 'keyword',
    icon: 'FileSearchOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
})

const table = ref()
const tableOptions = {
  columns: [{
    title: '封面',
    type: 'image',
    dataIndex: '_cover'
  }, {
    title: '标题',
    dataIndex: 'title'
  }, {
    title: '作者',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime'
  }]
}

const load = (count, index) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    const _promise = _filters.type === '文章'
      ? BannerApi.articles(count, index, _filters.keyword, {
        showLoading: false,
        toast: {
          success: false
        }
      })
      : BannerApi.activities(count, index, _filters.keyword, {
        showLoading: false,
        toast: {
          success: false
        }
      })

    _promise.then(result => {
      result.data.records.forEach(i => {
        i._articleId = i.id
        i._type = _filters.type === '文章' ? 'ARTICLE' : 'ACTIVITY'
        i._cover = Array.isArray(i.coverIds) && i.coverIds.length > 0 ? MediaApi.preview(i.coverIds[0]) : null
      })

      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const modal = ref()

const buttons = [{
  title: '确定',
  icon: '',
  type: 'primary',
  onClick () {
    const _selected = table.value.getSelectedRecords()
    console.log(_selected)

    const _promise = props.onOk(_selected.map(i => {
      return {
        articleId: i._articleId,
        type: i._type,
        title: i.title,
        cover: i._cover,
        body: i.body,
        creatorName: i.creatorName
      }
    }))

    // 关闭窗口
    _promise.then(() => {
      modal.value.close()
    })

    return _promise
  }
}]

const onShow = () => {
  nextTick(() => {
    table.value.load()
  })
}

const onClose = () => {
}

defineExpose({
  show () {
    modal.value.show()
  }
})
</script>

<template>
  <StatefulModal
    ref="modal"
    :buttons="buttons"
    :width="'calc(100vw * 0.8)'"
    @open="onShow"
    @close="onClose"
  >
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />

    <DataTable
      ref="table"
      :columns="tableOptions.columns"
      :layout="{
        showFilter: false
      }"
      :multiple="true"
      :load="load"
    />
  </StatefulModal>
</template>

<style lang="less">
.card-selected {
  border: 1px solid #52c41a;

  > .ant-card-cover {
    border: 1px solid #52c41a;
  }
}
</style>

<style lang="less" scoped>
.ant-pagination {
  margin: 16px 0;
  display: flex;
  justify-content: flex-end;
}
</style>
