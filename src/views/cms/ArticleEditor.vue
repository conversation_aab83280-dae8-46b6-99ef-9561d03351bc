<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import Editor from '@/components/ArticleEditor.vue'
import ArticleApi from '@/api/cms/article.js'
import MediaApi from '@/api/media/record.js'

const { proxy } = getCurrentInstance()

// 公共字段
const fields = [{
  title: '标题',
  field: 'title',
  type: 'text',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '封面',
  field: '_covers',
  type: 'file',
  config: {
    component: 'gallery',
    label: null,
    max: 1,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false
    },
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}, {
  title: '类型',
  field: 'subtype',
  type: 'select',
  config: {
    rules: [{
      required: true,
      message: '此内容必须输入'
    }]
  }
}]

// 图文字段
const richTextFields = [
  ...fields, {
    title: '正文',
    field: 'body',
    type: 'editor',
    config: {
      rules: [{
        required: true,
        message: '此内容必须输入'
      }]
    }
  }, {
    title: '地址',
    field: '_location',
    type: 'location',
    config: {
      disabled: false
    }
  }, {
    title: '附件',
    field: '_attachments',
    type: 'file',
    config: {
      max: Number.MAX_VALUE,
      showUploadList: {
        showPreviewIcon: false,
        showRemoveIcon: true,
        showDownloadIcon: true
      }
    }
  }]

// 视频号字段
const channelFields = [
  ...fields, {
    title: '视频号',
    field: 'finderUserName',
    type: 'text',
    config: {
      rules: [{
        required: true,
        message: '此内容必须输入'
      }]
    }
  }, {
    title: '视频号视频id',
    field: 'feedId',
    type: 'text',
    config: {
      rules: [{
        required: true,
        message: '此内容必须输入'
      }]
    }
  }]

const convert = record => {
  const _record = {
    ...record
  }
  delete _record._location
  delete _record._covers
  delete _record._attachments

  if (record._location === null) {
    _record.address = null
    _record.lng = null
    _record.lat = null
  } else {
    _record.address = record._location.address
    _record.lng = record._location.location.lng
    _record.lat = record._location.location.lat
  }

  _record.coverIds = record._covers === null ? null : record._covers.ids
  _record.attachmentIds = record._attachments === null ? null : record._attachments.ids

  return _record
}

const read = id => {
  return ArticleApi.get(id, {
    loading: true,
    toast: {
      success: false
    }
  })
}

const updateModelAfterRead = record => {
  const _record = {
    ...record
  }

  // 转换位置
  _record._location = {
    address: record.address,
    location: {
      lng: record.lng,
      lat: record.lat
    }
  }

  // 转换封面
  _record._covers = _record.coverIds
    ? {
        ids: _record.coverIds || [],
        files: (_record.coverIds || []).map(i => {
          return {
            id: i,
            name: '',
            url: MediaApi.preview(i)
          }
        })
      }
    : null

  // 转换附件
  _record._attachments = _record.attachments
    ? {
        ids: (_record.attachments || []).map(i => i.id),
        files: (_record.attachments || []).map(i => {
          return {
            id: i.id,
            name: i.name,
            url: MediaApi.preview(i.id)
          }
        })
      }
    : null

  return _record
}

const save = record => {
  return ArticleApi.save(convert(record))
}

const remove = record => {
  const _promise = ArticleApi.remove(record.id)
  _promise.then(() => {
    proxy.$router.replace({
      name: 'admin.cms.article.index'
    })
  })

  return _promise
}

const submit = record => {
  const _record = convert(record)
  _record.status = 'EDIT'

  return ArticleApi.save(_record)
}

const audit = {
  permission: 'cms:article:audit',
  callback (record, status) {
    return ArticleApi.audit(record.id, status)
  }
}

const retract = {
  permission: 'cms:article:retract',
  callback (record) {
    return ArticleApi.retract(record.id)
  }
}

const ready = ref(false)

onMounted(async () => {
  // 读取文章类型
  ArticleApi.subtypes({
    loading: true,
    toast: {
      success: false
    }
  })
    .then(result => {
      const _options = result.data.map(i => {
        return {
          label: i.val,
          value: i.id
        }
      })

      richTextFields[2].config.options = channelFields[2].config.options = _options

      ready.value = true
    })
})
</script>

<template>
  <div class="layout-content-panel">
    <template v-if="ready">
      <Editor
        :rich-text-fields="richTextFields"
        :channel-fields="channelFields"
        :read="read"
        :update-model-after-read="updateModelAfterRead"
        :save="save"
        :submit="submit"
        :remove="remove"
        :audit="audit"
        :retract="retract"
      />
    </template>
  </div>
</template>
