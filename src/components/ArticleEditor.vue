<script setup>
import { getCurrentInstance, inject, nextTick, onMounted, ref, watch } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import { isFunction } from '@/utils/object-util.js'
import PermissionApi from '@/api/sec/permission.js'
import FeedbackUtil from '@/utils/feedback.js'

const props = defineProps({
  theme: {
    type: Object,
    default: null
  },
  // 富文本表单字段
  richTextFields: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  // 视频号表单字段
  channelFields: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  // 读取
  read: {
    type: Function,
    default: null
  },
  // 读取后更新表单
  updateModelAfterRead: {
    type: Function,
    default: record => {
      return record
    }
  },
  // 保存
  save: {
    type: Function,
    default: null
  },
  // 提交
  submit: {
    type: Function,
    default: null
  },
  // 删除
  remove: {
    type: Function,
    default: null
  },
  // 审核
  audit: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      permission: '',
      callback: null
    }
  },
  // 撤稿
  retract: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      permission: '',
      callback: null
    }
  },
  // 归档
  archive: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      permission: '',
      callback: null
    }
  }

})

const { proxy } = getCurrentInstance()

const activeKey = ref(1)

const labelCol = {
  sm: {
    span: 4
  }
}

const wrapperCol = {
  sm: {
    span: 20
  }
}

// 图文
const richText = ref()

// 视频号
const channel = ref()

const readonly = ref(false)
watch(
  () => readonly.value,
  val => {
    props.richTextFields.forEach(i => {
      i.config.disabled = val
    })

    props.channelFields.forEach(i => {
      i.config.disabled = val
    })
  }
)

const reloadPage = inject('reloadPage')

const save = isFunction(props.save)
  ? record => {
    const _promise = props.save(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
  : null

const remove = isFunction(props.remove)
  ? record => {
    const _promise = props.remove(record)

    _promise.then(() => {
      proxy.$router.replace({
        name: 'admin.cms.article.index'
      })
    })

    return _promise
  }
  : null

const submit = isFunction(props.submit)
  ? {
      title: '提交',
      icon: 'UploadOutlined',
      callback (record) {
        const _promise = props.submit(record)

        _promise.then(result => {
          if (record.id == null) {
            proxy.$router.replace({
              query: {
                id: result.data
              }
            })
          } else {
            reloadPage()
          }
        })

        return _promise
      }
    }
  : null

const audit = isFunction(props.audit.callback)
  ? {
      title: '审核',
      icon: 'AuditOutlined',
      callback (record) {
        const _promise = new Promise(resolve => {
          FeedbackUtil.modal('是否发布该文章？', 'confirm', {
            closable: true,
            okText: '发布',
            onOk () {
              const _promise = props.audit.callback(record, 'PUBLISHED')
              _promise.finally(() => {
                resolve()
              })
              return _promise
            },
            cancelText: '退稿',
            onCancel () {
              const _promise = props.audit.callback(record, 'DRAFT')
              _promise.finally(() => {
                resolve()
              })
              return _promise
            }
          })
        })

        _promise.then(() => {
          reloadPage()
        })

        return _promise
      }
    }
  : null

const retract = isFunction(props.retract.callback)
  ? {
      title: '撤稿',
      icon: 'RollbackOutlined',
      callback (record) {
        const _promise = new Promise(resolve => {
          FeedbackUtil.modal('您即将撤回该文章，是否继续', 'confirm', {
            closable: true,
            onOk () {
              const _promise = props.retract.callback(record)
              _promise.finally(() => {
                resolve(true)
              })
              return _promise
            },
            onCancel () {
              resolve(false)
            }
          })
        })

        _promise.then(reload => {
          if (reload) {
            reloadPage()
          }
        })

        return _promise
      }
    }
  : null

const archive = isFunction(props.archive.callback)
  ? {
      title: '归档',
      icon: 'ArchiveOutlined',
      callback (record) {
        const _promise = new Promise(resolve => {
          FeedbackUtil.modal('您即将归档该文章，是否继续', 'confirm', {
            closable: true,
            onOk () {
              const _promise = props.archive.callback(record.id)
              _promise.finally(() => {
                resolve(true)
              })
              return _promise
            },
            onCancel () {
              resolve(false)
            }
          })
        })

        _promise.then(reload => {
          if (reload) {
            reloadPage()
          }
        })

        return _promise
      }
    }
  : null

const actions = []

const ready = ref(false)

onMounted(async () => {
  const _article = proxy.$route.query.id
    ? props.read(proxy.$route.query.id)
    : Promise.resolve()

  const _permissions = proxy.$route.query.id
    ? PermissionApi.me({
      loading: false,
      toast: {
        success: false
      }
    })
    : Promise.resolve()

  Promise.all([_article, _permissions])
    .then(results => {
      if (proxy.$route.query.id) {
        const _article = props.updateModelAfterRead(results[0].data)

        // 设置文章类型
        if (props.theme == null) {
          activeKey.value = _article.feedId == null ? 1 : 2
        } else {
          switch (props.theme.type) {
            case 'guide':
              activeKey.value = ['镇/街道', '村/社区'].indexOf(_article.subtype) === -1 ? 2 : 1
              break
          }
        }

        const _permissions = results[1].data
        switch (_article.status) {
          case 'DRAFT':
            // 仅作者允许编辑
            if (_article.isAuthor) {
              actions.push(isFunction(save)
                ? {
                    callback: save
                  }
                : null)

              actions.push(isFunction(remove)
                ? {
                    callback: remove
                  }
                : null)

              if (submit !== null) {
                actions.push(submit)
              }
            } else {
              readonly.value = true
            }

            break
          case 'EDIT':
            readonly.value = true

            if (_permissions.filter(i => new RegExp(i.name).test(props.audit.permission)).length > 0) {
              // 仅审核员允许编辑
              actions.push(null)

              actions.push(isFunction(remove)
                ? {
                    callback: remove
                  }
                : null)

              if (audit !== null) {
                actions.push(audit)
              }
            }

            break
          case 'PUBLISHED':
            readonly.value = true

            if (_permissions.filter(i => new RegExp(i.name).test(props.retract.permission)).length > 0) {
              actions.push(null)

              actions.push(isFunction(remove)
                ? {
                    callback: remove
                  }
                : null)

              if (retract !== null) {
                actions.push(retract)
              }

              if (archive !== null) {
                actions.push(archive)
              }
            }

            break
        }

        ready.value = true

        // 设置文章内容
        nextTick(() => {
          switch (activeKey.value) {
            case 1:
              richText.value.setModel(_article)
              break
            case 2:
              channel.value.setModel(_article)
              break
          }
        })
      } else {
        actions.push(isFunction(save)
          ? {
              callback: save
            }
          : null)

        actions.push(null)

        if (submit !== null) {
          actions.push(submit)
        }
      }

      ready.value = true
    })
})
</script>

<template>
  <div class="layout-content-panel">
    <a-tabs
      v-model:active-key="activeKey"
    >
      <template v-if="ready">
        <template v-if="props.richTextFields.length > 0">
          <a-tab-pane
            :key="1"
            :tab="props.theme === null ? '图文' : props.theme.tabs
              .filter(i => i.key === 1)
              .map(i => i.title)[0]"
            :disabled="activeKey !== 1 && readonly"
          >
            <GenericFrom
              ref="richText"
              :fields="props.richTextFields"
              :actions="actions"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
            />
          </a-tab-pane>
        </template>
        <template v-if="props.channelFields.length > 0">
          <a-tab-pane
            :key="2"
            :tab="props.theme === null ? '视频号' : props.theme.tabs
              .filter(i => i.key === 2)
              .map(i => i.title)[0]"
            :disabled="activeKey !== 2 && readonly"
          >
            <GenericFrom
              ref="channel"
              :fields="props.channelFields"
              :actions="actions"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
            />
          </a-tab-pane>
        </template>
      </template>
    </a-tabs>
  </div>
</template>
