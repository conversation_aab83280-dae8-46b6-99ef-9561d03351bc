<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { AiEditor } from 'aieditor'
import 'aieditor/dist/style.css'
import FeedbackUtil from '@/utils/feedback.js'
import { isFunction } from '@/utils/object-util.js'

const props = defineProps({
  height: {
    type: String,
    default: '600px'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  ai: {
    type: Object,
    default: null
  },
  toolbar: {
    type: Array,
    default: () => [
      'undo', 'redo', 'brush', 'eraser',
      '|', 'heading', 'font-family', 'font-size',
      '|', 'bold', 'italic', 'underline', 'strike', 'link', 'code', 'subscript', 'superscript', 'hr', 'todo', 'emoji',
      '|', 'highlight', 'font-color',
      '|', 'align', 'line-height',
      '|', 'bullet-list', 'ordered-list', 'indent-decrease', 'indent-increase', 'break',
      '|', 'image', 'video', 'attachment', 'quote', 'code-block', 'table',
      '|', 'source-code', 'printer', 'fullscreen', 'ai'
    ]
  },
  upload: {
    type: Function,
    default: null
  },
  onChange: {
    type: Function,
    default: null
  }
})

const toolbar = props.toolbar
if (props.ai === null) {
  const _index = toolbar.indexOf('ai')
  if (_index !== -1) {
    toolbar.splice(_index, 1)
  }
}

const editor = ref()

let component = null

const getMarkdown = () => {
  return component.getMarkdown()
}

const getHtml = () => {
  return component.getHtml()
}

const getText = () => {
  return component.getText()
}

const setContent = content => {
  component.setEditable(true)
  component.setContent(content)
  component.setEditable(!props.disabled)
}

const setEditable = editable => {
  component.setEditable(editable)
}

defineExpose({
  getHtml,
  getMarkdown,
  getText,
  setContent,
  setEditable
})

onMounted(() => {
  component = new AiEditor({
    element: editor.value,
    editable: !props.disabled,
    draggable: false,
    ai: {
      onCreateClientUrl (modelName, modelConfig, startFn) {
        if (props.ai && isFunction((props.ai.apiUrl))) {
          props.ai.apiUrl(modelName, modelConfig).then(url => {
            startFn(url)
          })
        }
      },
      onTokenConsume (modelName, modelConfig, count) {
      }
    },
    image: {
      allowBase64: true,
      defaultSize: 350,
      uploader: (file, uploadUrl, headers, formName) => {
        return isFunction(props.upload) ? props.upload('image', file) : Promise.resolve(false)
      },
      uploaderEvent: {
        onSuccess (file, response) {
          return response.errorCode === 0
        },
        onFailed (file, response) {
          console.log(response)

          FeedbackUtil.notification(response.message, 'error')
        },
        onError (file, error) {
          console.log(error)

          FeedbackUtil.notification(error.message, 'error')
        }
      },
      bubbleMenuItems: ['AlignLeft', 'AlignCenter', 'AlignRight', 'delete']
    },
    video: {
      uploader: (file, uploadUrl, headers, formName) => {
        return isFunction(props.upload) ? props.upload('video', file) : Promise.resolve(false)
      },
      uploaderEvent: {
        onSuccess (file, response) {
          return response.errorCode === 0
        },
        onFailed (file, response) {
          FeedbackUtil.notification(response.message, 'error')
        },
        onError (file, error) {
          FeedbackUtil.notification(error.message, 'error')
        }
      }
    },
    attachment: {
      uploader: (file, uploadUrl, headers, formName) => {
        return isFunction(props.upload) ? props.upload('attachment', file) : Promise.resolve(false)
      },
      uploaderEvent: {
        onSuccess: (file, response) => {
          return response.errorCode === 0
        },
        onFailed: (file, response) => {
          console.log(response)

          FeedbackUtil.notification(response.message, 'error')
        },
        onError: (file, error) => {
          console.log(error)

          FeedbackUtil.notification(error.message, 'error')
        }
      }
    },
    toolbarKeys: toolbar,
    onChange (editor) {
      if (isFunction(props.onChange)) {
        props.onChange(editor.getHtml(), editor.getText())
      }
    }
  })
})

onUnmounted(() => {
  component.destroy()
})
</script>

<template>
  <div
    ref="editor"
    :style="{
      height: props.height
    }"
  />
</template>
