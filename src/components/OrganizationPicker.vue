<script setup>
import { computed, ref, watch } from 'vue'
import DepartmentApi from '@/api/sys/department.js'
import UserApi from '@/api/sys/user.js'

const props = defineProps({
  id: {
    type: String,
    default: null
  },
  // 部门属性映射器
  departmentMapper: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      id: 'id',
      name: 'name',
      code: 'code',
      fullName: 'fullName',
      hasSubordinates: 'hasSubordinates'
    }
  },
  // 用户属性映射器
  userMapper: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      id: 'id',
      name: 'name',
      deptId: 'deptId',
      deptName: 'deptName',
      deptCode: 'deptCode',
      deptFullName: 'deptFullName'
    }
  },
  title: {
    type: String,
    default: null
  },
  type: {
    type: String,
    default: 'all'
  },
  max: {
    type: Number,
    default: Number.MAX_VALUE
  },
  loadOrganization: {
    type: Function,
    default: DepartmentApi.organization
  },
  suggestUsers: {
    type: Function,
    default: UserApi.suggest
  }
})

const organization = ref({
  current: {
    id: null,
    code: null,
    name: null,
    fullName: null
  },
  members: []
})

// 导航栏
const navIds = computed(() => {
  return organization.value.current.code === null ? [] : organization.value.current.code.split('.')
})
const navNames = computed(() => {
  return organization.value.current.fullName === null ? [] : organization.value.current.fullName.split('/')
})

const loadOrganization = rootId => {
  props.loadOrganization(rootId, {
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    organization.value.current = result.data.department
    organization.value.members = []

    result.data.subordinates.forEach(i => {
      const _department = {
        _type_: 'department',
        _check_: false,
        id: i[props.departmentMapper.id],
        name: i[props.departmentMapper.name],
        code: i[props.departmentMapper.code],
        fullName: i[props.departmentMapper.fullName],
        hasSubordinates: i[props.departmentMapper.hasSubordinates]
      }

      organization.value.members.push(_department)
    })

    if (props.type !== 'department') {
      result.data.members.forEach(i => {
        const _user = {
          _type_: 'user',
          _check_: false,
          id: i[props.userMapper.id],
          name: i[props.userMapper.name],
          deptId: i[props.userMapper.deptId],
          deptName: i[props.userMapper.deptName],
          deptCode: i[props.userMapper.deptCode],
          deptFullName: i[props.userMapper.deptFullName]
        }

        organization.value.members.push(_user)
      })
    }
  })
}

const input = ref({
  value: [],
  showLoading: false,
  options: []
})

const suggestUsers = key => {
  if (props.type === 'department') {
    return
  }

  input.value.loading = true

  props.suggestUsers(5, key, {
    showLoading: false,
    toast: {
      success: false
    }
  })
    .then(result => {
      input.value.options = []
      result.data.forEach(i => {
        const _user = {
          id: i[props.userMapper.id],
          name: i[props.userMapper.name],
          deptId: i[props.userMapper.deptId],
          deptName: i[props.userMapper.deptName],
          deptCode: i[props.userMapper.deptCode],
          deptFullName: i[props.userMapper.deptFullName]
        }

        input.value.options.push({
          label: _user.name,
          value: _user
        })
      })
    }).finally(() => {
      input.value.loading = false
    })
}

const deselect = (value, option) => {
  organization.value.members
    .filter(i => {
      return i.id === value.id && i._type_ === value._type_
    })
    .forEach(i => {
      i._check_ = false
    })
}

const selectUser = (value, option) => {
  if (input.value.value.length >= props.max) {
    return
  }

  const _user = {
    _type_: 'user',
    ...value
  }

  select(_user, 'user')
}

const select = (value, type) => {
  const _value = {
    _type_: type
  }

  if (type === 'department') {
    _value.id = value[props.departmentMapper.id]
    _value.name = value[props.departmentMapper.name]
    _value.code = value[props.departmentMapper.code]
    _value.fullName = value[props.departmentMapper.fullName]
    _value.hasSubordinates = value[props.departmentMapper.hasSubordinates]
  } else {
    _value.id = value[props.userMapper.id]
    _value.name = value[props.userMapper.name]
    _value.deptId = value[props.userMapper.deptId]
    _value.deptName = value[props.userMapper.deptName]
    _value.deptCode = value[props.userMapper.deptCode]
    _value.deptFullName = value[props.userMapper.deptFullName]
  }

  // 加入选中
  input.value.value.push({
    label: _value.name,
    value: _value
  })

  // 设置勾选
  organization.value.members
    .filter(i => {
      return i.id === _value.id && i._type_ === type
    })
    .forEach(i => {
      i._check_ = true
    })
}

const check = ({ target }, record) => {
  if (input.value.value.length >= props.max && target.checked) {
    record._check_ = false
    return
  }

  // 设置勾选
  record._check_ = target.checked

  if (target.checked) {
    // 加入选中
    input.value.value.push({
      label: record.name,
      value: record
    })
  } else {
    // 移除选中
    for (let i = 0; i < input.value.value.length; i++) {
      if (record.id === input.value.value[i].value.id && record._type_ === input.value.value[i].value._type_) {
        input.value.value.splice(i, 1)
        break
      }
    }
  }
}

const open = ref(false)
// 关闭时清理数据
watch(
  () => open.value,
  value => {
    if (value === false) {
      organization.value.current.id = null
      organization.value.current.code = null
      organization.value.current.name = null
      organization.value.current.fullName = null
      organization.value.members = []

      input.value.value = []
      input.value.options = []
    }
  }
)

const show = () => {
  open.value = true

  loadOrganization(null)
}

const emits = defineEmits(['picked'])
const ok = () => {
  const _departments = []
  input.value.value
    .filter(({ value }) => value._type_ === 'department')
    .forEach(({ value }) => {
      const _department = {}
      _department[props.departmentMapper.id] = value.id
      _department[props.departmentMapper.name] = value.name
      _department[props.departmentMapper.code] = value.code
      _department[props.departmentMapper.fullName] = value.fullName

      _departments.push(_department)
    })

  const _users = []
  input.value.value
    .filter(({ value }) => value._type_ === 'user')
    .forEach(({ value }) => {
      const _user = {}
      _user[props.userMapper.id] = value.id
      _user[props.userMapper.name] = value.name
      _user[props.userMapper.deptId] = value.deptId
      _user[props.userMapper.deptName] = value.deptName
      _user[props.userMapper.deptCode] = value.deptCode
      _user[props.userMapper.deptFullName] = value.deptFullName

      _users.push(_user)
    })

  emits('picked', _departments, _users, props.id)

  open.value = false
}

defineExpose({
  show,
  select
})
</script>

<template>
  <a-modal
    v-model:open="open"
    :cancel-text="'取消'"
    :centered="true"
    :closable="false"
    :destroy-on-close="true"
    :ok-text="'确定'"
    :title="title"
    :width="'50%'"
    :wrap-class-name="'full-screen'"
    @ok="ok"
  >
    <a-card
      :size="'small'"
      style="height: 100%"
    >
      <!-- 搜索栏 -->
      <a-select
        v-model:value="input.value"
        :mode="'multiple'"
        :options="input.options"
        :style="{ width: '100%' }"
        @deselect="deselect"
        @search="suggestUsers"
        @select="selectUser"
      >
        <template
          v-if="input.loading"
          #notFoundContent
        >
          <a-spin :size="'small'" />
        </template>
      </a-select>

      <!-- 导航栏 -->
      <a-breadcrumb style="padding: 8px">
        <a-breadcrumb-item>
          <a @click="loadOrganization(null)">
            <home-outlined />
          </a>
        </a-breadcrumb-item>
        <template v-if="navIds.length > 0">
          <template
            v-for="(i, index) in navIds"
            :key="i"
          >
            <a-breadcrumb-item>
              <a @click="loadOrganization(i)">
                {{ navNames[index] }}
              </a>
            </a-breadcrumb-item>
          </template>
        </template>
      </a-breadcrumb>

      <!-- 列表 -->
      <a-list
        :data-source="organization.members"
        :item-layout="'horizontal'"
      >
        <template #renderItem="{ item }">
          <div style="padding: 8px; border-top: 1px solid #f0f0f0; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; justify-content: space-between;">
            <!-- 部门节点 -->
            <template v-if="item._type_ === 'department'">
              <a
                style="text-overflow: ellipsis; overflow: hidden;"
                @click="loadOrganization(item[departmentMapper.id])"
              >
                <bank-outlined />
                {{ item[departmentMapper.name] }}
              </a>

              <template v-if="type !== 'user'">
                <a-checkbox
                  v-model:checked="item._check_"
                  :value="item.id"
                  @change="check($event, item)"
                />
              </template>
            </template>

            <!-- 人员节点 -->
            <template v-else>
              <div>
                <user-outlined />
                {{ item[userMapper.name] }}
              </div>
              <a-checkbox
                v-model:checked="item._check_"
                :value="item.id"
                @change="check($event, item)"
              />
            </template>
          </div>
        </template>
      </a-list>
    </a-card>
  </a-modal>
</template>

<style scoped lang="less">
.ant-list-items {
  > div:last-child {
    border-bottom: none!important;
  }
}
</style>
