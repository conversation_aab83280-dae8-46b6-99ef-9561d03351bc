<script setup>
import { getCurrentInstance, provide, ref, watch } from 'vue'
import AdminLayout from '@/layouts/AdminLayout.vue'
import Unauthorized from '@/views/exception/403.vue'
import { keepAliveComponents } from '@/router/index.js'

const { proxy } = getCurrentInstance()

const tab = ref({
  activeKey: '',
  animated: false,
  panes: []
})

const close = key => {
  edit(key, 'remove')
}
provide('closePage', close)

// 刷新tab
const reload = () => {
  const _query = {
    ...proxy.$router.currentRoute.value.query,
    _t: +new Date()
  }

  proxy.$router.replace({
    query: _query
  })
}
provide('reloadPage', reload)

// 关闭其它tab页
const closeOther = () => {
  const _activeKey = tab.value.activeKey
  tab.value.panes = tab.value.panes.filter(i => i.key === _activeKey)
}

// 关闭tab页
const edit = (key, action) => {
  if (action === 'remove') {
    for (let i = 0; i < tab.value.panes.length; i++) {
      if (tab.value.panes[i].key === key) {
        // 关闭的是当前页，导航到前一页
        if (tab.value.activeKey === key) {
          tab.value.activeKey = tab.value.panes[i === 0 ? 1 : i - 1].key
        }

        // 删除tab页
        tab.value.panes.splice(i, 1)

        break
      }
    }
  }
}

const showDefaultView = route => {
  // 支持匿名访问
  if (route.meta && route.meta.anon) {
    return true
  }

  // 未读取页面权限则通过
  if (proxy.$store.state.assets.pages === null) {
    return true
  }

  // 已授权
  return proxy.$store.state.assets.pages.filter(i => i.path === route.path).length > 0
}

// 监听activeKey变化，切换路由
watch(
  () => tab.value.activeKey,
  value => {
    const _panes = tab.value.panes.filter(i => i.key === value)
    if (_panes.length === 0) {
      return
    }

    // 切换路由
    proxy.$router.push(_panes[0].route)
  }
)

// 监听路由变化，切换tab页
watch(
  () => proxy.$route,
  value => {
    const _panes = tab.value.panes.filter(i => i.key === value.path)
    if (_panes.length === 0) {
      tab.value.panes.push({
        key: value.path,
        title: value.meta ? value.meta.title : null,
        closable: tab.value.panes.length > 0,
        route: value
      })
    } else {
      // 可能更新了路由
      _panes[0].route = value
    }

    tab.value.activeKey = value.path
  }, {
    immediate: true
  }
)

const namedComponent = (component, route) => {
  if (component) {
    component.type.name = route.name
  }

  return component
}
</script>

<template>
  <AdminLayout>
    <template #header>
      <div class="layout-tabs-header">
        <a-tabs
          v-model:active-key="tab.activeKey"
          :animated="tab.animated"
          :destroy-inactive-tab-pane="false"
          :hide-add="true"
          :type="'editable-card'"
          @edit="edit"
        >
          <template #rightExtra>
            <a-dropdown>
              <a
                class="ant-dropdown-link"
                @click.prevent
              >
                <ellipsis-outlined :rotate="'90'" />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="reload">
                    <a>刷新</a>
                  </a-menu-item>
                  <a-menu-item @click="closeOther">
                    <a>关闭其它</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>

          <template
            v-for="i in tab.panes"
            :key="i.key"
          >
            <a-tab-pane
              :closable="tab.panes.length > 1"
              :tab="i.title"
            />
          </template>
        </a-tabs>
      </div>
    </template>

    <template #body>
      <router-view v-slot="{ Component, route }">
        <!-- 默认视图 -->
        <template v-if="showDefaultView(route)">
          <keep-alive :include="keepAliveComponents">
            <component
              :is="namedComponent(Component, route)"
              :key="route.fullPath"
            />
          </keep-alive>
        </template>

        <!-- 未授权 -->
        <template v-else>
          <Unauthorized />
        </template>
      </router-view>
    </template>
  </AdminLayout>
</template>

<style lang="less">
.layout-tabs-header {
  padding-top: 6px;
  background-color: #fff;
  z-index: 100;

  .ant-tabs-nav {
    padding: 0 16px;
  }

  > .ant-tabs > .ant-tabs-content-holder > .ant-tabs-content {
    margin-top: -16px;
    // min-height: 120px;

    > .ant-tabs-tabpane {
      background: #fff;
    }
  }
}
</style>
